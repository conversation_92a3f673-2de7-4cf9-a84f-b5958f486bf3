import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { MailerService } from '@nestjs-modules/mailer';
import { SendGiftEmailDto } from './dto/send-gift-email.dto';

@Injectable()
export class GiftsService {
  private readonly logger = new Logger(GiftsService.name);

  constructor(
    private readonly mailerService: MailerService,
    private readonly configService: ConfigService,
  ) {}

  async sendGiftEmail(sendGiftEmailDto: SendGiftEmailDto): Promise<{ success: boolean; message: string }> {
    const { email } = sendGiftEmailDto;

    try {
      // Generate unique message ID
      const messageId = `gift-${Date.now()}-${Math.random().toString(36).substr(2, 9)}@kitsify.com`;

      const subject = '🎁 Your Special Gift from Kitsify - Premium Tools Access!';
      
      const htmlContent = `
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Your Special Gift from Kitsify</title>
          <style>
            body {
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 600px;
              margin: 0 auto;
              padding: 20px;
              background-color: #f8f9fa;
            }
            .container {
              background: white;
              border-radius: 15px;
              padding: 30px;
              box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            }
            .header {
              text-align: center;
              margin-bottom: 30px;
              padding: 20px;
              background: linear-gradient(135deg, #ec4899, #8b5cf6);
              border-radius: 10px;
              color: white;
            }
            .gift-icon {
              font-size: 48px;
              margin-bottom: 10px;
            }
            .title {
              font-size: 28px;
              font-weight: bold;
              margin: 0;
            }
            .subtitle {
              font-size: 16px;
              opacity: 0.9;
              margin: 5px 0 0 0;
            }
            .benefits {
              background: #f8f9ff;
              border-radius: 10px;
              padding: 25px;
              margin: 25px 0;
            }
            .benefit-item {
              display: flex;
              align-items: center;
              margin: 15px 0;
              font-size: 16px;
            }
            .benefit-icon {
              font-size: 20px;
              margin-right: 12px;
              width: 30px;
            }
            .cta-section {
              text-align: center;
              margin: 30px 0;
              padding: 25px;
              background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
              border-radius: 10px;
            }
            .cta-button {
              display: inline-block;
              background: linear-gradient(135deg, #ec4899, #8b5cf6);
              color: white;
              text-decoration: none;
              padding: 15px 30px;
              border-radius: 25px;
              font-weight: bold;
              font-size: 18px;
              margin: 10px;
              transition: transform 0.2s;
            }
            .cta-button:hover {
              transform: translateY(-2px);
            }
            .instructions {
              background: #fff3cd;
              border: 1px solid #ffeaa7;
              border-radius: 8px;
              padding: 20px;
              margin: 20px 0;
            }
            .footer {
              text-align: center;
              margin-top: 30px;
              padding-top: 20px;
              border-top: 1px solid #e5e7eb;
              color: #6b7280;
              font-size: 14px;
            }
            .highlight {
              background: linear-gradient(120deg, #fbbf24 0%, #f59e0b 100%);
              color: white;
              padding: 2px 8px;
              border-radius: 4px;
              font-weight: bold;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <div class="gift-icon">🎁</div>
              <h1 class="title">Your Special Gift is Here!</h1>
              <p class="subtitle">Exclusive access to premium tools and resources</p>
            </div>
            
            <p>Hello there!</p>
            
            <p>Thank you for your interest in Kitsify! We're excited to share this special gift package with you.</p>
            
            <div class="benefits">
              <h3 style="margin-top: 0; color: #4f46e5;">🎉 Your Gift Package Includes:</h3>
              
              <div class="benefit-item">
                <span class="benefit-icon">🚀</span>
                <span><strong>7-Day Premium Access</strong> - Full access to all premium tools</span>
              </div>
              
              <div class="benefit-item">
                <span class="benefit-icon">📚</span>
                <span><strong>Exclusive E-commerce Guides</strong> - Professional strategies and tips</span>
              </div>
              
              <div class="benefit-item">
                <span class="benefit-icon">🎨</span>
                <span><strong>High-Converting Ad Templates</strong> - 250+ proven templates</span>
              </div>
              
              <div class="benefit-item">
                <span class="benefit-icon">💎</span>
                <span><strong>VIP Community Access</strong> - Connect with successful entrepreneurs</span>
              </div>
            </div>
            
            <div class="cta-section">
              <h3 style="margin-top: 0;">Ready to Get Started?</h3>
              <p>Click the button below to access your gift and start exploring:</p>
              <a href="https://kitsify.com/tools" class="cta-button">🎁 Claim Your Gift Now</a>
            </div>
            
            <div class="instructions">
              <h4 style="margin-top: 0;">📋 How to Get Started:</h4>
              <ol>
                <li><strong>Install the Chrome Extension:</strong> Visit <a href="https://chrome.google.com/webstore" target="_blank">Chrome Web Store</a> and search for "Kitsify"</li>
                <li><strong>Visit Our Dashboard:</strong> Go to <a href="https://kitsify.com/tools" target="_blank">kitsify.com/tools</a></li>
                <li><strong>Start Exploring:</strong> Use your <span class="highlight">7-day premium access</span> to try all our tools</li>
              </ol>
            </div>
            
            <p><strong>Why Choose Kitsify?</strong></p>
            <ul>
              <li>✅ <strong>All-in-One Solution:</strong> 25+ premium tools in one package</li>
              <li>✅ <strong>One-Click Access:</strong> No more password management hassles</li>
              <li>✅ <strong>Huge Savings:</strong> Save over $3,000/month compared to individual subscriptions</li>
              <li>✅ <strong>24/7 Support:</strong> Our team is always ready to help</li>
            </ul>
            
            <p>Don't miss this opportunity to supercharge your online business! This special gift is our way of welcoming you to the Kitsify community.</p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="https://kitsify.com/tools" class="cta-button">🚀 Start Your Journey Now</a>
            </div>
            
            <p>If you have any questions or need assistance, feel free to reach out to our support team at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
            
            <p>Welcome to Kitsify! 🌟</p>
            
            <p>Best regards,<br>
            <strong>The Kitsify Team</strong></p>
            
            <div class="footer">
              <p>This email was sent to ${email}</p>
              <p>© ${new Date().getFullYear()} Kitsify. All rights reserved.</p>
              <p><a href="https://kitsify.com" target="_blank">Visit our website</a> | <a href="https://discord.gg/kitsify" target="_blank">Join our Discord</a></p>
            </div>
          </div>
        </body>
        </html>
      `;

      const mailFrom = this.configService.get('mail.from');

      await this.mailerService.sendMail({
        to: email,
        from: {
          name: 'Kitsify',
          address: mailFrom,
        },
        subject: subject,
        html: htmlContent,
        headers: {
          'Message-ID': messageId,
          'List-Unsubscribe': `<https://kitsify.com/unsubscribe>, <mailto:<EMAIL>>`,
          'List-Unsubscribe-Post': 'List-Unsubscribe=One-Click',
          'Feedback-ID': `gift-email:kitsify:${process.env.MAIL_FEEDBACK_ID}`,
          Precedence: 'bulk',
          'X-Auto-Response-Suppress': 'OOF, AutoReply',
          'X-Entity-Ref-ID': `kitsify-gift-email-${Date.now()}`,
        },
      });

      this.logger.log(`Gift email sent successfully to ${email}`);
      
      return {
        success: true,
        message: 'Gift email sent successfully! Please check your inbox.',
      };
    } catch (error) {
      this.logger.error(`Failed to send gift email to ${email}:`, error);
      
      return {
        success: false,
        message: 'Failed to send gift email. Please try again later.',
      };
    }
  }
}
